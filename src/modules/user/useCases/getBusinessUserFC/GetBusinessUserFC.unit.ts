import { expect, beforeEach, it } from 'vitest';
import { Request } from './GetBusinessUserFCDTOs';
import { BusinessUsersInDb } from '@shared/infra/database/sequelize/migrations/BusinessUsers.json';
import { BusinessUserRepoFake } from '../../repos/BusinessUserRepoFake';
import { GetBusinessUserFC } from './GetBusinessUserFC';
import { nonExistingId } from '@shared/utils/test';
import { BusinessUserRepo } from '../../repos/BusinessUserRepo';
import { getVerificationData } from '../../utils/test';

let businessUserRepo, getBusinessUser: GetBusinessUserFC;
beforeEach(() => {
  businessUserRepo = new BusinessUserRepoFake();
  getBusinessUser = new GetBusinessUserFC(businessUserRepo);
});

it(`gets a user`, async () => {
  const validData: Request = {
    id: BusinessUsersInDb[0].id,
  };

  const response = await getBusinessUser.executeImpl(validData);

  const businessDto = BusinessUserRepo.mapDb2Dto(BusinessUsersInDb[0]);
  expect(response).toEqual({
    ...businessDto,
    ...getVerificationData(businessDto),
  });
});

it(`returns null when id isn't found`, async () => {
  const response = await getBusinessUser.executeImpl({
    id: nonExistingId,
  });

  expect(response).toBe(null);
});
