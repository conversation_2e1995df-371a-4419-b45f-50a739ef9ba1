import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import {
  dateFormat,
  expectErrorAppSync,
  expectMultipleErrorsAppSync,
  nonExistingId,
  orNull,
  pickLng,
} from '@shared/utils/test';
import { createBusinessUser, removeBusinessUser } from '../../utils/test';
import { BusinessUserRepo } from '../../repos/BusinessUserRepo';
import { getTimezone } from '@shared/core/test';
import { BusinessUsersInDb } from '@shared/infra/database/sequelize/migrations/BusinessUsers.json';
import { Request } from './UpdateBusinessUserDTOs';
import { BusinessUser } from '../../domain/BusinessUser';
import { BusinessUserForFrontendFragment } from '../../utils/fragments';
import { Name } from '../../domain/Name';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const appsync = new AppSyncClient();
const repo = new BusinessUserRepo(models.BusinessUser);

const query = gql`
  mutation (
    $id: ID!
    $firstName: String
    $lastName: String
    $userLng: String!
    $lng: String!
    $timezone: String!
  ) {
    updateBusinessUser(
      id: $id
      firstName: $firstName
      lastName: $lastName
      userLng: $userLng
      lng: $lng
      timezone: $timezone
    ) {
      result {
        ...BusinessUserForFrontendFragment
      }
      time
    }
  }
  ${BusinessUserForFrontendFragment}
`;

it(`updates user`, async () => {
  // First create a customer
  const { user } = createBusinessUser();
  await repo.create(user);

  const userLng = pickLng();
  const input = {
    id: user.id.toString(),
    firstName: orNull(chance.string({ alpha: true, numeric: true, length: 15 })),
    lastName: orNull(chance.string({ alpha: true, numeric: true, length: 15 })),
    userLng,
    lng: userLng === 'en'? 'es' : 'en',
    timezone: getTimezone(),
  };

  const received = await appsync.send<Request>({
    query,
    variables: input,
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  if (!json.data.updateBusinessUser)
    console.log('UpdateBusinessUser.e2e.ts 1', json);
  const response = json.data.updateBusinessUser;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { userLng: _userLng, ...rest } = input; // exclude userLng, since returns as BusinessUSer.lng
  const updated = {
    ...BusinessUser.forFrontend(user.toDto()),
    ...rest,
    lng: userLng,
  };
  expect(response).toEqual({
    result: updated,
    time: expect.stringMatching(dateFormat),
  });

  const saved = await repo.get(user.id.toString());
  expect(saved?.toDto()).toMatchObject(updated);

  await removeBusinessUser(user.id.toString());
});

it(`fails returning multiple errors in fields`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: {
      id: BusinessUsersInDb[0].id,
      firstName: 'a'.repeat(Name.maxLength + 1),
      lastName: 'a'.repeat(Name.maxLength + 1),
      userLng: pickLng(),
      lng: pickLng(),
      timezone: '',
    },
  });

  expect(received.status).toBe(200);
  expectMultipleErrorsAppSync({
    response: await received.json(),
    errors: [
      {
        field: 'firstName',
        type: expect.any(String),
        message: expect.any(String),
        status: 400,
      },
      {
        field: 'lastName',
        type: expect.any(String),
        message: expect.any(String),
        status: 400,
      },
      {
        field: 'timezone',
        type: expect.any(String),
        message: expect.any(String),
        status: 400,
      },
    ],
  });
});

it(`fails when user isn't found`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: {
      id: nonExistingId,
      firstName: chance.string({ alpha: true, numeric: true, length: 15 }),
      lastName: chance.string({ alpha: true, numeric: true, length: 15 }),
      userLng: pickLng(),
      lng: pickLng(),
      timezone: getTimezone(),
    },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  console.log('UpdateBusinessUser.e2e.ts 2', json);
  expectErrorAppSync({
    response: json,
    query: 'updateBusinessUser',
    error: 'UpdateBusinessUserErrors.NotFound',
    status: 404,
  });
});
