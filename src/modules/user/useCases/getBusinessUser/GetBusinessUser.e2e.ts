import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { dateFormat, nonExistingId } from '@shared/utils/test';
import { BusinessUsersInDb } from '@shared/infra/database/sequelize/migrations/BusinessUsers.json';
import { Request } from './GetBusinessUserDTOs';
import { BusinessUserRepo } from '../../repos/BusinessUserRepo';
import { BusinessUser } from '../../domain/BusinessUser';
import { BusinessUserForFrontendFragment } from '../../utils/fragments';

const appsync = new AppSyncClient();

it(`gets a customer`, async () => {
  const received = await appsync.send<Request>({
    query: gql`
      query ($id: ID!) {
        getBusinessUser(id: $id) {
          result {
            ...BusinessUserForFrontendFragment
          }
          time
        }
      }
      ${BusinessUserForFrontendFragment}
    `,
    variables: {
      id: BusinessUsersInDb[0].id,
    },
  });

  expect(received.status).toBe(200);
  const response = (await received.json()).data.getBusinessUser;
  const businessForFrontend = BusinessUser.forFrontend(BusinessUserRepo.mapDb2Dto(BusinessUsersInDb[0]));
  expect(response).toEqual({
    result: {
      ...businessForFrontend,
      phoneVerification: {
        ...businessForFrontend.phoneVerification,
        codeExpiresAt: expect.stringMatching(businessForFrontend.phoneVerification.codeExpiresAt!.replace('.000', '')),
      },
      emailVerification: {
        ...businessForFrontend.emailVerification,
        codeExpiresAt: expect.stringMatching(businessForFrontend.emailVerification.codeExpiresAt!.replace('.000', '')),
      },
    },
    time: expect.stringMatching(dateFormat),
  });
});

it(`returns null when the customer isn't found`, async () => {
  const received = await appsync.send<Request>({
    query: gql`
      query ($id: ID!) {
        getBusinessUser(id: $id) {
          result {
            __typename
          } # To avoid error, I need to put something here even when I'll receive null
          time
        }
      }
    `,
    variables: {
      id: nonExistingId,
    },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  expect(json.data.getBusinessUser).toMatchObject({
    result: null,
    time: expect.stringMatching(dateFormat),
  });
});
