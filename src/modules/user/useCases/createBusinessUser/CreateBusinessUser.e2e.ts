import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import {
  dateFormat,
  expectErrorAppSync,
  pickLng,
} from '@shared/utils/test';
import { BusinessUsersInDb } from '@shared/infra/database/sequelize/migrations/BusinessUsers.json';
import { getTimezone } from '@shared/core/test';
import { uuidFormat } from '@shared/utils/utils';
import { removeBusinessUser } from '../../utils/test';
import {
  BalanceRepo,
  GSforReservationRepo,
  rmBalanceRow,
  rmGSforReservation,
} from '../../utils/testExternal';
import { Request } from './CreateBusinessUserDTOs';
import { Movement } from '../../../reservation/utils/testExternal';
import { BusinessUserForFrontendFragment } from '../../utils/fragments';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const appsync = new AppSyncClient();

const input = {
  lng: pickLng(),
  email: chance.email(),
  timezone: getTimezone(),
};
const defaultProps = {
  firstName: null,
  lastName: null,
  phone: null,
};
const success = {
  ...input,
  ...defaultProps,
  id: expect.stringMatching(uuidFormat),
}

const query = gql`
  mutation (
    $lng: String!
    $email: String!
    $timezone: String!
  ) {
    createBusinessUser(
      lng: $lng
      email: $email
      timezone: $timezone
    ) {
      result {
        ...BusinessUserForFrontendFragment
      }
      time
    }
  }
  ${BusinessUserForFrontendFragment}
`;

it(`creates a user`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: input,
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  const response = json.data.createBusinessUser;
  expect(response).toMatchObject({
    result: success,
    time: expect.stringMatching(dateFormat),
  });
  const businessId = response.result.id;
  await removeBusinessUser(businessId);

  // Side effects on reservation module
  const gsR = await GSforReservationRepo.get(businessId);
  expect(gsR).not.toBeNull();
  await rmGSforReservation(businessId);

  // Side effects on balance module
  const balanceRows = await BalanceRepo.list(businessId);
  const matches = balanceRows.filter(row => {
    return row.userId === businessId &&
      row.movement === Movement.INITIAL_FREE_BALANCE;
  });
  expect(matches).toHaveLength(1);
  await rmBalanceRow({ userId: businessId, index: 0});
});

it(`returns error when email is already taken`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: {
      ...input,
      email: BusinessUsersInDb[0].email,
    },
  });

  expect(received.status).toBe(200);
  const response = await received.json();
  expectErrorAppSync({
    response,
    query: 'createBusinessUser',
    error: 'CreateBusinessUserErrors.EmailAlreadyTaken',
    status: 409,
  });
});
