import {
  StackContext,
  Function,
  AppSync<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ron,
  // @ts-expect-error
} from 'sst/constructs';
import * as appsync from 'aws-cdk-lib/aws-appsync';
import * as cdk from 'aws-cdk-lib';
import { getCalendarCreds, getDbCreds } from './helpers';
import { Code, BaseDataSource } from 'aws-cdk-lib/aws-appsync';
import bundleAppSyncResolver from '@shared/infra/iac/bundlerAppSyncResolver';

export async function MyStack({ stack, app }: StackContext) {
  // region get creds from ssm
  const { app: dbCreds, analytics: dbAnalyticsCreds } = await getDbCreds(app);
  const calendarCreds = await getCalendarCreds(app);
  // endregion

  const sendAnalyticsEvent = new Function(stack, 'sendAnalyticsEvent', {
    handler:
      'src/modules/analytics/useCases/send/index.handler',
    environment: {
      ...dbAnalyticsCreds,
    },
  });

  const distributeDomainEvents = new Function(stack, 'distributeDomainEvents', {
    handler: 'src/shared/infra/invocation/DistributeDomainEvents.handler',
  });
  allowSendingAnalytics(distributeDomainEvents);
  const dummySubscriber = new Function(stack, 'dummySubscriber', {
    handler: 'src/shared/infra/invocation/dummy/index.handler',
  });
  allowSubscribeToDomainEvents(dummySubscriber, 'dummySubscriber');

  // region user module
  const createCustomerUser = new Function(stack, 'createCustomerUser', {
    handler: 'src/modules/user/useCases/createCustomerUser/index.handler',
    environment: dbCreds,
  });
  allowSendingAnalytics(createCustomerUser);
  allowEmittingDomainEvents(createCustomerUser);
  const createCustomerManually = new Function(stack, 'createCustomerManually', {
    handler: 'src/modules/user/useCases/createCustomerManually/index.handler',
    environment: dbCreds,
  });
  allowEmittingDomainEvents(createCustomerManually);
  allowSendingAnalytics(createCustomerManually);
  const updateCustomerManually = new Function(stack, 'updateCustomerManually', {
    handler: 'src/modules/user/useCases/updateCustomerManually/index.handler',
    environment: dbCreds,
  });
  allowSendingAnalytics(updateCustomerManually);
  const deleteCustomerManually = new Function(stack, 'deleteCustomerManually', {
    handler: 'src/modules/user/useCases/deleteCustomerManually/index.handler',
    environment: dbCreds,
  });
  allowSendingAnalytics(deleteCustomerManually);
  const getCustomerUser = new Function(stack, 'getCustomerUser', {
    handler: 'src/modules/user/useCases/getCustomerUser/index.handler',
    environment: dbCreds,
  });
  allowSendingAnalytics(getCustomerUser);
  const getBusinessUser = new Function(stack, 'getBusinessUser', {
    handler: 'src/modules/user/useCases/getBusinessUser/index.handler',
    environment: dbCreds,
  });
  allowSendingAnalytics(getBusinessUser);
  const getManuallyCreatedCustomer = new Function(
    stack,
    'getManuallyCreatedCustomer',
    {
      handler:
        'src/modules/user/useCases/getManuallyCreatedCustomer/index.handler',
      environment: dbCreds,
    },
  );
  allowSendingAnalytics(getManuallyCreatedCustomer);
  const getManuallyCreatedCustomers = new Function(
    stack,
    'getManuallyCreatedCustomers',
    {
      handler:
        'src/modules/user/useCases/getManuallyCreatedCustomers/index.handler',
      environment: dbCreds,
    },
  );
  allowSendingAnalytics(getManuallyCreatedCustomers);
  const createBusinessUser = new Function(stack, 'createBusinessUser', {
    handler: 'src/modules/user/useCases/createBusinessUser/index.handler',
    environment: dbCreds,
  });
  allowSendingAnalytics(createBusinessUser);
  allowEmittingDomainEvents(createBusinessUser);
  const updateBusinessUser = new Function(stack, 'updateBusinessUser', {
    handler: 'src/modules/user/useCases/updateBusinessUser/index.handler',
    environment: dbCreds,
  });
  allowSendingAnalytics(updateBusinessUser);

  const changeBusinessPhoneSendCode = new Function(stack, 'changeBusinessPhoneSendCode', {
    handler: 'src/modules/user/useCases/changeBusinessPhoneSendCode/index.handler',
    environment: dbCreds,
  });
  allowSendingAnalytics(changeBusinessPhoneSendCode);

  const changeBusinessPhoneConfirmCode = new Function(stack, 'changeBusinessPhoneConfirmCode', {
    handler: 'src/modules/user/useCases/changeBusinessPhoneConfirmCode/index.handler',
    environment: dbCreds,
  });
  allowSendingAnalytics(changeBusinessPhoneConfirmCode);
  // endregion

  // region calendar module
  const checkMakeChangesAndSave = new Function(stack, 'checkMakeChangesAndSave', {
    handler: 'src/modules/calendar/useCases/checkMakeChangesAndSave/index.handler',
    environment: {
      ...calendarCreds,
      ...dbCreds,
    },
  });
  allowSendingAnalytics(checkMakeChangesAndSave);

  const deleteCalendar = new Function(stack, 'deleteCalendar', {
    handler: 'src/modules/calendar/useCases/deleteCalendar/index.handler',
    environment: dbCreds,
  });
  allowSendingAnalytics(deleteCalendar);

  const getCalendars = new Function(stack, 'getCalendars', {
    handler: 'src/modules/calendar/useCases/getCalendars/index.handler',
    environment: {
      ...calendarCreds,
      ...dbCreds,
    },
  });
  allowSendingAnalytics(getCalendars);
  // endregion

  // region reservation module
  // region GS
  const createGSforReservation = new Function(stack, 'createGSforReservation', {
    handler:
      'src/modules/reservation/useCases/createGSforReservation/index.handler',
    environment: {
      ...dbCreds,
    },
  });
  allowSubscribeToDomainEvents(createGSforReservation, 'createGSforReservation');
  allowSendingAnalytics(createGSforReservation);

  const updateGSreservations = new Function(
    stack,
    'updateGSreservations',
    {
      handler:
        'src/modules/reservation/useCases/updateGSreservations/index.handler',
      environment: dbCreds,
    },
  );
  allowSendingAnalytics(updateGSreservations);
  const updateGSstoreInfo = new Function(stack, 'updateGSstoreInfo', {
    handler: 'src/modules/reservation/useCases/updateGSstoreInfo/index.handler',
    environment: dbCreds,
  });
  allowSendingAnalytics(updateGSstoreInfo);

  const getGSforReservations = new Function(stack, 'getGSforReservations', {
    handler: 'src/modules/reservation/useCases/getGSforReservations/index.handler',
    environment: dbCreds,
  });
  allowSendingAnalytics(getGSforReservations);
  // endregion

  //region Reservation ops
  const setActiveReservationOption = new Function(
    stack,
    'setActiveReservationOption',
    {
      handler:
        'src/modules/reservation/useCases/setActiveReservationOption/index.handler',
      environment: {
        ...dbCreds,
      },
    },
  );
  allowSendingAnalytics(setActiveReservationOption);

  const getAvailabilityOfReservationOptions = new Function(
    stack,
    'getAvailabilityOfReservationOptions',
    {
      handler:
        'src/modules/reservation/useCases/getAvailabilityOfReservationOptions/index.handler',
      environment: {
        ...dbCreds,
        ...calendarCreds,
      },
    },
  );
  allowSendingAnalytics(getAvailabilityOfReservationOptions);
  // endregion
  // region Reservation crud
  const createReservation = new Function(stack, 'createReservation', {
    handler: 'src/modules/reservation/useCases/createReservation/index.handler',
    environment: {
      ...dbCreds,
      ...calendarCreds,
    },
  });
  allowSendingAnalytics(createReservation);
  allowEmittingDomainEvents(createReservation);

  const createReservationByBusiness = new Function(
    stack,
    'createReservationByBusiness',
    {
      handler:
        'src/modules/reservation/useCases/createReservationByBusiness/index.handler',
      environment: {
        ...dbCreds,
        ...calendarCreds,
      },
    },
  );
  allowSendingAnalytics(createReservationByBusiness);
  allowEmittingDomainEvents(createReservationByBusiness);

  const cancelReservationByBusiness = new Function(
    stack,
    'cancelReservationByBusiness',
    {
      handler:
        'src/modules/reservation/useCases/cancelReservationByBusiness/index.handler',
      environment: {
        ...dbCreds,
        ...calendarCreds,
      },
    },
  );
  allowEmittingDomainEvents(cancelReservationByBusiness);
  allowSendingAnalytics(cancelReservationByBusiness);

  const cancelReservation = new Function(stack, 'cancelReservation', {
    handler: 'src/modules/reservation/useCases/cancelReservation/index.handler',
    environment: {
      ...dbCreds,
      ...calendarCreds,
    },
  });
  allowEmittingDomainEvents(cancelReservation);
  allowSendingAnalytics(cancelReservation);

  const setReminder = new Function(stack, 'setReminder', {
    handler: 'src/modules/reservation/useCases/setReminder/index.handler',
    environment: dbCreds,
  });
  allowSendingAnalytics(setReminder);

  const getReservations = new Function(stack, 'getReservations', {
    handler: 'src/modules/reservation/useCases/getReservations/index.handler',
    environment: dbCreds,
  });
  allowSendingAnalytics(getReservations);
  // endregion
  // region Reservation Option crud
  const createReservationOption = new Function(stack, 'createReservationOption', {
    handler:
      'src/modules/reservation/useCases/createReservationOption/index.handler',
    environment: dbCreds,
  });
  allowSendingAnalytics(createReservationOption);
  const getReservationOptions = new Function(stack, 'getReservationOptions', {
    handler:
      'src/modules/reservation/useCases/getReservationOptions/index.handler',
    environment: dbCreds,
  });
  allowSendingAnalytics(getReservationOptions);
  const updateReservationOption = new Function(stack, 'updateReservationOption', {
    handler:
      'src/modules/reservation/useCases/updateReservationOption/index.handler',
    environment: dbCreds,
  });
  allowSendingAnalytics(updateReservationOption);
  const deleteReservationOption = new Function(stack, 'deleteReservationOption', {
    handler:
      'src/modules/reservation/useCases/deleteReservationOption/index.handler',
    environment: dbCreds,
  });
  allowSendingAnalytics(deleteReservationOption);
  // endregion
  // endregion

  // region balance module
  const createBalance = new Function(stack, 'createBalance', {
    handler:
      'src/modules/balance/useCases/createBalance/index.handler',
    environment: {
      ...dbCreds,
    },
  });
  allowSendingAnalytics(createBalance);
  allowSubscribeToDomainEvents(createBalance, 'createBalance');
  const moveInvolvingReservation = new Function(stack, 'moveInvolvingReservation', {
    handler:
      'src/modules/balance/useCases/moveInvolvingReservation/index.handler',
    environment: {
      ...dbCreds,
    },
  });
  allowSendingAnalytics(moveInvolvingReservation);
  allowSubscribeToDomainEvents(moveInvolvingReservation, 'moveInvolvingReservation');
  const getBalanceRows = new Function(stack, 'getBalanceRows', {
    handler:
      'src/modules/balance/useCases/getBalanceRows/index.handler',
    environment: {
      ...dbCreds,
    },
  });
  allowSendingAnalytics(getBalanceRows);
  const purchase = new Function(stack, 'purchase', {
    handler:
      'src/modules/balance/useCases/purchase/index.handler',
    environment: {
      ...dbCreds,
    },
  });
  allowSendingAnalytics(purchase);
  // Add an alarm when lambdas approach timeout. If 15 minutes isn't enough, check https://v2.sst.dev/long-running-jobs
  const monthlyTopUp = new Function(stack, 'monthlyTopUp', {
    handler:
      'src/modules/balance/useCases/monthlyTopUp/index.handler',
    environment: {
      ...dbCreds,
    },
    timeout: '15 minutes',
  });
  allowSendingAnalytics(monthlyTopUp);
  new Cron(stack, 'monthlyTopUpCron', { // All cron expressions use the UTC+0 time zone (https://v2.sst.dev/cron-jobs)

    // schedule: 'cron(0 0 1 * ? *)',  // Run at 0:00 am every 1st day of the month
    // For testing
    schedule: 'cron(0 0 1 * ? *)',  // Run at 0:00 am every day
    // schedule: 'cron(0/2 * * * ? *)',  // Run every 2 minutes for testing

    job: monthlyTopUp,
  });
  // endregion

  // region analyticsFromFrontend
  const analyticsFromFrontend = new Function(stack, 'analyticsFromFrontend', {
    handler:
      'src/modules/analytics/useCases/analyticsFromFrontend/index.handler',
    environment: {
      ...dbAnalyticsCreds,
    },
  });
  allowSendingAnalytics(analyticsFromFrontend);
  // endregion

  const api = new AppSyncApi(stack, 'AppSyncApi', {
    schema: 'src/shared/infra/appsync/schema.graphql',
    cdk: {
      graphqlApi: {
        authorizationConfig: {
          defaultAuthorization: {
            authorizationType: appsync.AuthorizationType.API_KEY,
            apiKeyConfig: {
              expires: cdk.Expiration.after(cdk.Duration.days(365)),
            },
          },
        },
        logConfig: {
          fieldLogLevel: appsync.FieldLogLevel.ALL,
        },
      },
    },
  });

  const adaptResult = bundleAppSyncResolver(
    'src/shared/infra/appsync/templates/adaptResult.ts',
  );

  // region add resolvers

  // region balance module
  addResolverWithLambda('Query', 'getBalanceRows', adaptResult, getBalanceRows);
  addResolverWithLambda('Mutation', 'purchase', adaptResult, purchase);
  // end

  // region user module
  addResolverWithLambda(
    'Mutation',
    'createCustomerUser',
    adaptResult,
    createCustomerUser,
  );
  addResolverWithLambda(
    'Mutation',
    'createCustomerManually',
    adaptResult,
    createCustomerManually,
  );
  addResolverWithLambda(
    'Mutation',
    'updateCustomerManually',
    adaptResult,
    updateCustomerManually,
  );
  addResolverWithLambda(
    'Mutation',
    'deleteCustomerManually',
    adaptResult,
    deleteCustomerManually,
  );
  addResolverWithLambda('Query', 'getCustomerUser', adaptResult, getCustomerUser);
  addResolverWithLambda('Query', 'getBusinessUser', adaptResult, getBusinessUser);
  addResolverWithLambda(
    'Query',
    'getManuallyCreatedCustomer',
    adaptResult,
    getManuallyCreatedCustomer,
  );
  addResolverWithLambda(
    'Query',
    'getManuallyCreatedCustomers',
    adaptResult,
    getManuallyCreatedCustomers,
  );
  addResolverWithLambda(
    'Mutation',
    'createBusinessUser',
    adaptResult,
    createBusinessUser,
  );
  addResolverWithLambda(
    'Mutation',
    'updateBusinessUser',
    adaptResult,
    updateBusinessUser,
  );
  addResolverWithLambda(
    'Mutation',
    'changeBusinessPhoneSendCode',
    adaptResult,
    changeBusinessPhoneSendCode,
  );
  addResolverWithLambda(
    'Mutation',
    'changeBusinessPhoneConfirmCode',
    adaptResult,
    changeBusinessPhoneConfirmCode,
  );
  // endregion

  // region calendar module
  addResolverWithLambda(
    'Mutation',
    'checkMakeChangesAndSave',
    adaptResult,
    checkMakeChangesAndSave,
  );
  addResolverWithLambda('Mutation', 'deleteCalendar', adaptResult, deleteCalendar);
  addResolverWithLambda('Query', 'getCalendars', adaptResult, getCalendars);
  // endregion

  // region reservation module
  // region GS
  addResolverWithLambda(
    'Mutation',
    'updateGSreservations',
    adaptResult,
    updateGSreservations,
  );
  addResolverWithLambda(
    'Mutation',
    'updateGSstoreInfo',
    adaptResult,
    updateGSstoreInfo,
  );
  addResolverWithLambda(
    'Query',
    'getGSforReservations',
    adaptResult,
    getGSforReservations,
  );
  // endregion
  // region Reservation ops
  addResolverWithLambda(
    'Mutation',
    'setActiveReservationOption',
    adaptResult,
    setActiveReservationOption,
  );
  addResolverWithLambda(
    'Query',
    'getAvailabilityOfReservationOptions',
    adaptResult,
    getAvailabilityOfReservationOptions,
  );
  addResolverWithLambda(
    'Mutation',
    'setReminder',
    adaptResult,
    setReminder,
  );
  // endregion
  // region Reservation Option crud
  addResolverWithLambda(
    'Mutation',
    'createReservationOption',
    adaptResult,
    createReservationOption,
  );
  addResolverWithLambda(
    'Query',
    'getReservationOptions',
    adaptResult,
    getReservationOptions,
  );
  addResolverWithLambda(
    'Mutation',
    'updateReservationOption',
    adaptResult,
    updateReservationOption,
  );
  addResolverWithLambda(
    'Mutation',
    'deleteReservationOption',
    adaptResult,
    deleteReservationOption,
  );
  // endregion
  // region Reservation crud
  addResolverWithLambda(
    'Mutation',
    'createReservation',
    adaptResult,
    createReservation,
  );
  addResolverWithLambda(
    'Mutation',
    'createReservationByBusiness',
    adaptResult,
    createReservationByBusiness,
  );
  addResolverWithLambda(
    'Mutation',
    'cancelReservationByBusiness',
    adaptResult,
    cancelReservationByBusiness,
  );
  addResolverWithLambda(
    'Mutation',
    'cancelReservation',
    adaptResult,
    cancelReservation,
  );
  addResolverWithLambda('Query', 'getReservations', adaptResult, getReservations);
  // endregion
  // endregion

  // region analyticsFromFrontend with direct asynchronous invocation of lambda
  const analyticsResolver = bundleAppSyncResolver(
    'src/shared/infra/appsync/templates/analyticsResolver.ts',
  );
  api.addDataSources(stack, {
    analyticsFromFrontendDS: analyticsFromFrontend,
  })
  new appsync.Resolver(stack, 'analyticsFromFrontendR', {
    api: api.cdk.graphqlApi,
    typeName: 'Mutation',
    fieldName: 'analyticsFromFrontend',
    code: analyticsResolver,
    runtime: appsync.FunctionRuntime.JS_1_0_0,
    dataSource: api.getDataSource('analyticsFromFrontendDS') as BaseDataSource,
  });
  // end
  // endregion

  // region helper functions
  function addResolverWithLambda(
    type: string,
    field: string,
    resolverAdapter: Code,
    // eslint-disable-next-line @typescript-eslint/ban-types
    lambda: Function,
  ) {
    const prefix = type + field;
    const ds = prefix + 'DS';
    api.addDataSources(stack, {
      [ds]: lambda,
    });
    new appsync.Resolver(stack, prefix + 'R', {
      api: api.cdk.graphqlApi,
      typeName: type,
      fieldName: field,
      code: resolverAdapter,
      runtime: appsync.FunctionRuntime.JS_1_0_0,
      pipelineConfig: [getAppsyncFunctionForLambda(prefix + 'AF', ds, stack)],
    });
  }
  function getAppsyncFunctionForLambda(
    name: string,
    datasource: string,
    stack: Stack,
  ) {
    return new appsync.AppsyncFunction(stack, name, {
      name,
      api: api.cdk.graphqlApi,
      dataSource: api.getDataSource(datasource) as BaseDataSource,
    });
  }
  function allowInvokingLambda(args: {
    // eslint-disable-next-line @typescript-eslint/ban-types
    caller: Function,
    // eslint-disable-next-line @typescript-eslint/ban-types
    callee: Function,
    calleeName: string,
  }) {
    const { caller, callee, calleeName } = args;
    callee.grantInvoke(caller);
    caller.addEnvironment(
      calleeName,
      callee.functionName,
    );
  }
  // eslint-disable-next-line @typescript-eslint/ban-types
  function allowEmittingDomainEvents(lambda: Function) {
    allowInvokingLambda({
      caller: lambda,
      callee: distributeDomainEvents,
      calleeName: 'distributeDomainEvents',
    });
  }
  // eslint-disable-next-line @typescript-eslint/ban-types
  function allowSubscribeToDomainEvents(lambda: Function, envVar: string) {
    lambda.grantInvoke(distributeDomainEvents); // distributeDomainEvents can invoke lambda
    distributeDomainEvents.addEnvironment(envVar, lambda.functionName);
  }

  // eslint-disable-next-line @typescript-eslint/ban-types
  function allowSendingAnalytics(lambda: Function) {
    allowInvokingLambda({
      caller: lambda,
      callee: sendAnalyticsEvent,
      calleeName: 'sendAnalyticsEvent',
    });
  }
  // endregion
}
