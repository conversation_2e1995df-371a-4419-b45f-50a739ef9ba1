'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        'business_users',
        'emailVerificationStatus',
        {
          type: DataTypes.STRING,
          allowNull: false,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'business_users',
        'emailVerificationAttempts',
        {
          type: DataTypes.ARRAY(DataTypes.DATE),
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'business_users',
        'emailVerificationBlockedUntil',
        {
          type: DataTypes.DATE,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'business_users',
        'emailVerificationNewEmail',
        {
          type: DataTypes.TEXT,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'business_users',
        'emailVerificationCode',
        {
          type: DataTypes.INTEGER,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'business_users',
        'emailVerificationCodeExpiresAt',
        {
          type: DataTypes.DATE,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn(
        'business_users',
        'emailVerificationStatus',
        { transaction },
      );

      await queryInterface.removeColumn(
        'business_users',
        'emailVerificationAttempts',
        { transaction },
      );

      await queryInterface.removeColumn(
        'business_users',
        'emailVerificationBlockedUntil',
        { transaction },
      );

      await queryInterface.removeColumn(
        'business_users',
        'emailVerificationNewEmail',
        { transaction },
      );

      await queryInterface.removeColumn(
        'business_users',
        'emailVerificationCode',
        { transaction },
      );

      await queryInterface.removeColumn(
        'business_users',
        'emailVerificationCodeExpiresAt',
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
