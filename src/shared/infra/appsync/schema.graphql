type Query {
  # user
  getBusinessUser(id: ID!): GetBusinessUserResponse!
  getCustomerUser(id: ID!): GetCustomerResponse!
  getManuallyCreatedCustomer(id: ID!, createdBy: ID!): GetManuallyCreatedCustomerResponse!
  getManuallyCreatedCustomers(createdBy: ID!): GetManuallyCreatedCustomersResponse!
  # reservation
  getReservationOptions(userId: ID!): GetReservationOptionsResponse!
  getReservations(userId: ID!): GetReservationsResponse!
  getGSforReservations(userId: ID!): GSforReservationsResponse!
  getAvailabilityOfReservationOptions(userId: ID!, lng: String!): GetAvailabilityOfReservationOptionsResponse!
  # calendar
  getCalendars(userId: ID!): GetCalendarsResponse!
  # balance
  getBalanceRows(userId: ID!): GetBalanceRowsResponse!
}
type Mutation {
  analyticsFromFrontend(
    userId: ID
    uiLang: String!
    label: String!
    date: String!
    appState: AWSJSON!
    ua: AWSJSON!
    data: AWSJSON!
  ): AnalyticsFromFrontendResponse!

  # user
  createBusinessUser(
    lng: String!
    email: String!
    timezone: String!
  ): CreateBusinessUserResponse!
  updateBusinessUser(
    id: ID!
    firstName: String
    lastName: String
    userLng: String!
    lng: String!
    timezone: String!
  ): CreateBusinessUserResponse!
  changeBusinessPhoneSendCode(
    id: ID!
    newPhone: String!
    lng: String!
    timezone: String!
  ): ChangeBusinessPhoneSendCodeResponse!
  changeBusinessPhoneConfirmCode(
    id: ID!
    code: Int!
    lng: String!
  ): ChangeBusinessPhoneConfirmCodeResponse!
  createCustomerUser(
    firstName: String!
    lastName: String!
    email: String!
    timezone: String!
  ): CreateCustomerResponse!
  createCustomerManually(
    firstName: String
    lastName: String
    notes: String
    createdBy: ID!
  ): CreateManuallyCreatedCustomerResponse!
  updateCustomerManually(
    firstName: String
    lastName: String
    notes: String
    createdBy: ID!
    id: ID!
  ): UpdateManuallyCreatedCustomerResponse!
  deleteCustomerManually(
    createdBy: ID!
    id: ID!
  ): DeleteManuallyCreatedCustomerResponse!
  # reservation
  createReservationOption(
    userId: ID!
    name: String!
    service: ServiceInput!
    location: LocationInput!
    timeReference: TimeReferenceInput!
    explanatoryNotes: ExplanatoryNotesInput!
    active: Boolean!
    every: Int!
    lng: String!
  ): CreateReservationOptionResponse!
  updateReservationOption(
    id: ID!
    userId: ID!
    name: String!
    service: ServiceInput!
    location: LocationInput!
    timeReference: TimeReferenceInput!
    explanatoryNotes: ExplanatoryNotesInput!
    active: Boolean!
    every: Int!
    lng: String!
  ): UpdateReservationOptionResponse!
  setActiveReservationOption(
    id: ID!
    userId: ID!
    active: Boolean!
    lng: String!
  ): SetActiveReservationOptionResponse!
  deleteReservationOption(userId: ID!, id: ID!): OkResponse!
  createReservation(
    businessId: ID!
    customerId: ID!
    start: AWSDateTime!
    roId: ID!
    roVersion: Int!
    lng: String!
  ): CreateReservationResponse!
  createReservationByBusiness(
    businessId: ID!
    customerId: ID!
    start: AWSDateTime!
    roId: ID!
    roVersion: Int!
    lng: String!
  ): CreateReservationResponse!
  updateGSreservations(
    userId: ID!
    cancellationUpTo: Int!
    maxSimultaneousReservations: Int!
    phoneRequired: Boolean!
    maxDaysAhead: Int!
    minTimeBeforeService: Int!
    reminderTemplate: String!
    lng: String!
  ): GSforReservationsResponse!
  updateGSstoreInfo(
    userId: ID!
    title: String
    welcome: String
    slug: String
    lng: String!
  ): UpdateGSstoreInfoResponse!
  cancelReservation(
    id: ID!
    customerId: ID!
    reason: String
    lng: String!
  ): UpdatedReservation!
  cancelReservationByBusiness(
    id: ID!
    businessId: ID!
    reason: String
    lng: String!
  ): CancelReservationByBusinessResponse!
  setReminder(
    reservationId: ID!
    businessId: ID!
    newStatus: String!
    lng: String!
  ): UpdatedReservation!
  # calendar
  checkMakeChangesAndSave(userId: ID!, calendarId: ID!, lng: String!, type: String!): CheckResponse!
  deleteCalendar(userId: ID!, calendarId: ID!, type: String!, lng: String!): CountResponse!
  # balance
  purchase(userId: ID!, credits: Int!, lng: String!): BalanceRowWithTimestampsResponse!
}
# balance
type GetBalanceRowsResponse {
  result: [BalanceRowWithTimestamps!]!
  time: AWSDateTime!
}
type BalanceRowWithTimestamps {
  userId: ID!
  index: Int!
  balance: Int!
  movement: String!
  change: Int!
  date: AWSDateTime!

  reservationId: ID
  customerId: ID
  customerFullName: String
  srvName: String

  created_at: AWSDateTime!
  updated_at: AWSDateTime!
}
type BalanceRowWithTimestampsResponse {
  result: BalanceRowWithTimestamps!
  time: AWSDateTime!
}
# user
type Customer {
  id: ID!
  firstName: String!
  lastName: String!
  email: String!
  phone: String
  timezone: String!
}
type CreateCustomerResponse {
  result: Customer!
  time: AWSDateTime!
}
type BusinessUser {
  id: ID!
  firstName: String
  lastName: String
  lng: String!
  email: String!
  phone: String
  timezone: String!
  phoneVerification: PhoneVerification!
  emailVerification: EmailVerification!
}
type PhoneVerification {
  status: String!
  blockedUntil: String
  newPhone: String
  codeExpiresAt: String
}
type EmailVerification {
  status: String!
  blockedUntil: String
  newEmail: String
  codeExpiresAt: String
}
type ManuallyCreatedCustomer {
  id: ID!
  firstName: String
  lastName: String
  fullName: String!
  notes: String
  createdBy: ID!
}
type ManuallyCreatedCustomerWithTimestamps {
  id: ID!
  firstName: String
  lastName: String
  fullName: String!
  notes: String
  createdBy: ID!
  created_at: AWSDateTime!
  updated_at: AWSDateTime!
  deleted_at: AWSDateTime
}
type GetCustomerResponse {
  result: Customer
  time: AWSDateTime!
}
type GetManuallyCreatedCustomerResponse {
  result: ManuallyCreatedCustomerWithTimestamps
  time: AWSDateTime!
}
type GetManuallyCreatedCustomersResponse {
  result: [ManuallyCreatedCustomerWithTimestamps!]
  time: AWSDateTime!
}
type CreateManuallyCreatedCustomerResponse {
  result: ManuallyCreatedCustomer!
  time: AWSDateTime!
}
type UpdateManuallyCreatedCustomerResponse {
  result: ManuallyCreatedCustomerWithTimestamps!
  time: AWSDateTime!
}
type DeleteManuallyCreatedCustomerResponse {
  result: Int!
  time: AWSDateTime!
}
type GetBusinessUserResponse {
  result: BusinessUser
  time: AWSDateTime!
}
type CreateBusinessUserResponse {
  result: BusinessUser!
  time: AWSDateTime!
}

type ChangeBusinessPhoneSendCodeResponse {
  result: ChangeBusinessPhoneSendCodeResult!
  time: AWSDateTime!
}

type ChangeBusinessPhoneSendCodeResult {
  attemptsLeft: Int!
  user: BusinessUser!
}

type ChangeBusinessPhoneConfirmCodeResponse {
  result: BusinessUser!
  time: AWSDateTime!
}

# reservation
type UpdateGSstoreInfoResponse {
  result: String!
  time: AWSDateTime!
}
type CancelReservationByBusinessResult {
  action: String!
  reservation: ReservationWithTimestamps! # return the same value as the query getReservationsResponse to get the value updated on the frontend
}
type CancelReservationByBusinessResponse {
  result: CancelReservationByBusinessResult!
  time: AWSDateTime!
}
type BaseError {
  type: String!
  message: String!
  status: Int!
}
type Reservation {  # Used by mutations createReservation and createReservationByBusiness
  id: ID!
  start: AWSDateTime!
  customerType: String!
  cancelledBy: String
  cancelledDate: AWSDateTime
  cancellationReason: String
  cancelReservationEventErrors: [String!]
  userId: ID!
  customerId: ID!
  customerFirstName: String
  customerLastName: String
  customerPhone: String
  customerEmail: String
  eventId: ID!
  roId: ID!
  roZone: String!
  srvName: String!
  srvDescription: String
  srvDuration: Int!
  srvPrice: Float
  locName: String!
  locId: String!
  reminder: String!
}
type CreateReservationResponse {
  result: Reservation!
  time: AWSDateTime!
}
type ReservationWithTimestamps {  # Used by query getReservationsResponse. Reservation & Timestamps
  id: ID!
  start: AWSDateTime!
  customerType: String!
  cancelledBy: String
  cancelledDate: AWSDateTime
  cancellationReason: String
  cancelReservationEventErrors: [String!]
  userId: ID!
  customerId: ID!
  customerFirstName: String
  customerLastName: String
  customerPhone: String
  customerEmail: String
  eventId: ID!
  roId: ID!
  roZone: String!
  srvName: String!
  srvDescription: String
  srvDuration: Int!
  srvPrice: Float
  locName: String!
  locId: String!
  reminder: String!
  created_at: AWSDateTime!
  updated_at: AWSDateTime!
  deleted_at: AWSDateTime
}
type ReservationOption {
  id: ID!
  userId: ID!
  name: String!
  active: Boolean!
  every: Int!
  service: Service!
  location: Location!
  timeReference: TimeReference!
  explanatoryNotes: ExplanatoryNotes
  version: Int!
}
type ReservationOptionWithTimestamps {  # ReservationOption & Timestamps
  id: ID!
  userId: ID!
  name: String!
  active: Boolean!
  every: Int!
  version: Int!
  service: Service!
  location: Location!
  timeReference: TimeReference!
  explanatoryNotes: ExplanatoryNotes
  created_at: AWSDateTime!
  updated_at: AWSDateTime!
  deleted_at: AWSDateTime
}
type CreateReservationOptionResponse {
  result: ReservationOption!
  time: AWSDateTime!
}
type SetActiveReservationOptionResponse {
  result: ReservationOptionWithTimestamps!
  time: AWSDateTime!
}
type UpdateReservationOptionResponse {
  result: ReservationOptionWithTimestamps!
  time: AWSDateTime!
}
type GetReservationOptionsResponse {
  result: [ReservationOptionWithTimestamps!]
  time: AWSDateTime!
}
type TimeWithSpots {
  spots: Int!
  time: AWSDateTime!
}
type ReservationOptionWithTimestampsAndAvailability { # ReservationOptionWithTimestamps + availability
  id: ID!
  userId: ID!
  name: String!
  active: Boolean!
  every: Int!
  version: Int!
  service: Service!
  location: Location!
  timeReference: TimeReference!
  explanatoryNotes: ExplanatoryNotes
  created_at: AWSDateTime!
  updated_at: AWSDateTime!
  deleted_at: AWSDateTime
  availabilityGot: [TimeWithSpots!]
  availabilityErrors: [BaseError!]
}
type GetAvailabilityOfReservationOptionsResponse {
  result: [ReservationOptionWithTimestampsAndAvailability!]
  time: AWSDateTime!
}
type GetReservationsResponse {
  result: [ReservationWithTimestamps!]
  time: AWSDateTime!
}
type GSforReservations {
  id: ID!
  cancellationUpTo: Int!
  phoneRequired: Boolean!
  maxSimultaneousReservations: Int!
  reservationLimits: ReservationLimits!
  reminderTemplate: String!
  title: String
  welcome: String
  slug: String
}
type GSforReservationsResponse {
  result: GSforReservations!
  time: AWSDateTime!
}
type Service {
  name: String!
  description: String
  duration: Int!
  price: Float
}
input ServiceInput {
  name: String!
  description: String
  duration: Int!
  price: Float
}
type Location {
  name: String
  description: String
  identifier: String!
  showInfoWhenReserving: Boolean!
}
input LocationInput {
  name: String
  description: String
  identifier: String!
  showInfoWhenReserving: Boolean!
}
type TimeReference {
  hour: Int!
  minute: Int!
  zone: String!
}
input TimeReferenceInput {
  hour: Int!
  minute: Int!
  zone: String!
}
type ExplanatoryNotes {
  while: String
  after: String
}
input ExplanatoryNotesInput {
  while: String
  after: String
}

# calendar
type GetCalendarsResponse {
  result: Calendars!
  time: AWSDateTime!
}
type Calendars {
  availability: Calendar
  reservations: Calendar
}
type Calendar {
  name: String!
  id: ID!
}
type Slot {
  start: AWSDateTime!
  end: AWSDateTime!
}
type EventRetrieved {
  name: String
  description: String
  slot: Slot!
  id: ID!
}
type Detected {
  identifier: String!
  spots: Int!
}
type NextEvent {
  event: EventRetrieved!
  detected: [Detected!]
}
type CurrentCalendar {
  name: String!
  id: ID!
  nextEventsSearched: Boolean!
  nextEvents: [NextEvent!]
}
type Check {
  action: String!
  currentCalendar: CurrentCalendar
  messageError: String
  checkError: BaseError
}
type CheckResponse {
  result: Check!
  time: AWSDateTime!
}
type ReservationLimits {
  maxDaysAhead: Int!
  minTimeBeforeService: Int!
}

schema {
  query: Query
  mutation: Mutation
}
type OkResponse {
  result: Boolean!  # always true
  time: AWSDateTime!
}
type VoidResponse {
  time: AWSDateTime!
}

type AnalyticsFromFrontendResponse {
  statusCode: Int!
  message: String!
}

type UpdatedReservation {
  result: ReservationWithTimestamps!
  time: AWSDateTime!
}
type CountResponse {
  result: Int!
  time: AWSDateTime!
}